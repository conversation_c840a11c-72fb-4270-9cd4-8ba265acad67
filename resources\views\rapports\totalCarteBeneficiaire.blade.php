@extends('layouts.base')
@section('content')

<div class="layout-px-spacing">
    <div class="middle-content container-xxl p-0">
        <div id="tableCustomBasic" class="col-lg-12 col-12 layout-spacing">
            <div class="d-flex justify-content-between align-items-center flex-wrap mb-4 mt-5 p-3 rounded shadow-sm bg-white border">
                <h2 class="fw-bold mb-0 text-dark">Statistique des cartes par bénéficiaires</h2>
            </div>
            <div class="statbox widget box box-shadow">
                <div class="widget-content widget-content-area" id="printableArea">
                    <table id="zero-config" class="table style-1 dt-table-hover non-hover display">
                        <thead>
                            <tr>
                                <th class="" scope="col">Nom du Bénéficiaire</th>
                                <th scope="col">Cartes Générés</th>
                                <th class="" scope="col">Cartes Actives</th>
                                <th class="" scope="col">Cates Non Approuvées</th>
                                <th class="" scope="col">Cates Scannées</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($beneficiaires as $beneficiaire)
                            <tr>
                                <td class="">{{ $beneficiaire->RaisonSociale}} </td>
                                <td>
                                    <span class="table-inner-text"><b>{{ $beneficiaire->totalCartesGenerees()}}</b> </span>
                                </td>
                                <td class=""><b>{{ $beneficiaire->totalCartesActives() }}</b> </td>
                                <td class="">
                                    <b> {{ $beneficiaire->totalCartesNonApprouvees() }}</b>
                                </td>


                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
@section('js')
<script src="https://cdnjs.cloudflare.com/ajax/libs/sweetalert/2.1.0/sweetalert.min.js"></script>
<script type="text/javascript">
    $('.show_confirm').click(function(event) {
        var form = $(this).closest("form");
        var name = $(this).data("name");
        event.preventDefault();
        swal({
                title: `Etes-vous sûr de vouloir supprimer ?`
                , text: "Cette suppression est definitive de l'application"
                , icon: "warning"
                , buttons: true
                , buttons: ['Annuler', 'Confirmer']
                , dangerMode: true
            , })
            .then((willDelete) => {
                if (willDelete) {
                    form.submit();
                }
            });
    });
    // print button
    function printDiv(divName) {
        var printContents = document.getElementById(divName).innerHTML;
        var originalContents = document.body.innerHTML;
        document.body.innerHTML = printContents;
        window.print();

        document.body.innerHTML = originalContents;
    }

</script>
@endsection
